/**
 * <PERSON><PERSON><PERSON> to verify recent assessments and persona profiles
 */

const { Pool } = require('./auth-service/node_modules/pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'atma_db',
  user: 'atma_user',
  password: 'secret-passworrd'
});

async function verifyAssessments() {
  try {
    console.log('🔍 Verifying recent assessments and persona profiles...\n');

    // Count recent assessments
    const countResult = await pool.query(`
      SELECT
        COUNT(*) as total_assessments,
        COUNT(DISTINCT user_id) as unique_users
      FROM assessment.assessments
      WHERE created_at > NOW() - INTERVAL '1 hour'
    `);

    console.log('📊 Recent Assessments (last 1 hour):');
    console.log(`   Total Assessments: ${countResult.rows[0].total_assessments}`);
    console.log(`   Unique Users: ${countResult.rows[0].unique_users}`);
    console.log('');

    // Get latest assessments
    const latestResult = await pool.query(`
      SELECT
        user_id,
        status,
        created_at,
        queue_position
      FROM assessment.assessments
      WHERE created_at > NOW() - INTERVAL '1 hour'
      ORDER BY created_at DESC
      LIMIT 15
    `);

    console.log('📋 Latest 15 Assessments:');
    latestResult.rows.forEach((row, i) => {
      const time = new Date(row.created_at).toLocaleTimeString();
      console.log(`   ${i+1}. User ${row.user_id}: ${row.status} (Queue: ${row.queue_position || 'N/A'}) at ${time}`);
    });
    console.log('');

    // Check persona profiles
    const profileResult = await pool.query(`
      SELECT
        COUNT(*) as total_profiles,
        COUNT(DISTINCT user_id) as unique_users_with_profiles
      FROM assessment.persona_profiles
      WHERE created_at > NOW() - INTERVAL '1 hour'
    `);

    console.log('👤 Persona Profiles Generated (last 1 hour):');
    console.log(`   Total Profiles: ${profileResult.rows[0].total_profiles}`);
    console.log(`   Unique Users: ${profileResult.rows[0].unique_users_with_profiles}`);
    console.log('');

    // Get sample persona profiles
    const sampleProfiles = await pool.query(`
      SELECT
        user_id,
        primary_persona,
        confidence_score,
        created_at
      FROM assessment.persona_profiles
      WHERE created_at > NOW() - INTERVAL '1 hour'
      ORDER BY created_at DESC
      LIMIT 10
    `);

    console.log('🎭 Sample Persona Profiles:');
    sampleProfiles.rows.forEach((row, i) => {
      const time = new Date(row.created_at).toLocaleTimeString();
      console.log(`   ${i+1}. User ${row.user_id}: ${row.primary_persona} (${row.confidence_score}% confidence) at ${time}`);
    });

    // Summary
    const successRate = (profileResult.rows[0].total_profiles / countResult.rows[0].total_assessments * 100).toFixed(1);
    console.log('\n' + '='.repeat(60));
    console.log('📈 VERIFICATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Assessments Submitted: ${countResult.rows[0].total_assessments}`);
    console.log(`✅ Persona Profiles Generated: ${profileResult.rows[0].total_profiles}`);
    console.log(`📊 Success Rate: ${successRate}%`);
    
    if (successRate >= 95) {
      console.log('🎉 EXCELLENT: Load test successful with high success rate!');
    } else if (successRate >= 80) {
      console.log('✅ GOOD: Load test successful with acceptable success rate');
    } else {
      console.log('⚠️  WARNING: Low success rate, check analysis worker logs');
    }
    
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Database Error:', error.message);
  } finally {
    await pool.end();
  }
}

verifyAssessments();
