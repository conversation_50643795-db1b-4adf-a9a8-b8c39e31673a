{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 1, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:45:25.256Z", "stats": {"totalUsers": 1, "successfulLogins": 1, "failedLogins": 0, "successfulSubmissions": 0, "failedSubmissions": 1, "totalResponseTime": 276, "minResponseTime": 18, "maxResponseTime": 258, "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 18, "status": 400, "responseData": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"viaIs.appreciationOfBeauty": "VIA-IS assessment data is required"}}}}], "startTime": 1752817524898, "endTime": 1752817525253}, "results": [], "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 18, "status": 400, "responseData": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"viaIs.appreciationOfBeauty": "VIA-IS assessment data is required"}}}}]}