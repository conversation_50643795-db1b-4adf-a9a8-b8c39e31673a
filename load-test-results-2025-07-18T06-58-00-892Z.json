{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 10, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "mock", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T06:58:00.893Z", "stats": {"totalUsers": 10, "successfulLogins": 0, "failedLogins": 10, "successfulSubmissions": 0, "failedSubmissions": 0, "totalResponseTime": 0, "minResponseTime": null, "maxResponseTime": 0, "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 2, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 3, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 4, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 5, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 6, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 7, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 8, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 9, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 10, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}], "startTime": 1752821880803, "endTime": 1752821880890}, "results": [], "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 2, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 3, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 4, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 5, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 6, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 7, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 8, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 9, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}, {"userIndex": 10, "email": "<EMAIL>", "error": "Request failed with status code 503", "responseTime": 0, "status": 503, "responseData": {"success": false, "error": {"code": "SERVICE_UNAVAILABLE", "message": "Authentication service is temporarily unavailable"}}}]}