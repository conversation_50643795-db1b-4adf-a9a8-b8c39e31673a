@echo off
title ATMA Backend - Mock Mode Launcher

echo ========================================
echo ATMA Backend - Starting All Services (MOCK MODE)
echo ========================================
echo.

REM Configuration for Mock Mode
set WORKER_COUNT=5
set WORKER_CONCURRENCY=5
set USE_MOCK_MODEL=true
set DB_POOL_MAX=25
set DB_POOL_MIN=5
set DB_POOL_ACQUIRE=60000
set DB_POOL_IDLE=30000

echo Configuration:
echo - Number of Analysis Workers: %WORKER_COUNT%
echo - Worker Concurrency: %WORKER_CONCURRENCY% jobs per worker
echo - Total Processing Capacity: %WORKER_COUNT% x %WORKER_CONCURRENCY% = 25 concurrent jobs
echo - AI Mode: MOCK (No rate limits)
echo - Database Pool Max: %DB_POOL_MAX% connections
echo.

REM Kill existing processes (cleanup)
echo Cleaning up existing processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo ========================================
echo Starting Core Services...
echo ========================================

REM Start Auth Service
echo [1/6] Starting Auth Service...
start "Auth Service" cmd /k "cd /d %~dp0auth-service && npm start"
timeout /t 5 /nobreak >nul

REM Start Archive Service with increased DB pool
echo [2/6] Starting Archive Service (Enhanced DB Pool)...
start "Archive Service" cmd /k "cd /d %~dp0archive-service && set DB_POOL_MAX=25 && set DB_POOL_MIN=5 && set DB_POOL_ACQUIRE=60000 && set DB_POOL_IDLE=30000 && npm start"
timeout /t 5 /nobreak >nul

REM Start Assessment Service
echo [3/6] Starting Assessment Service...
start "Assessment Service" cmd /k "cd /d %~dp0assessment-service && npm start"
timeout /t 5 /nobreak >nul

REM Start Notification Service
echo [4/6] Starting Notification Service...
start "Notification Service" cmd /k "cd /d %~dp0notification-service && npm start"
timeout /t 5 /nobreak >nul

REM Start API Gateway
echo [5/6] Starting API Gateway...
start "API Gateway" cmd /k "cd /d %~dp0api-gateway && npm start"
timeout /t 5 /nobreak >nul

echo [6/6] Core services started!
echo.

echo ========================================
echo Starting Analysis Workers (MOCK MODE)...
echo ========================================

REM Start Analysis Worker 1
echo Starting Analysis Worker 1 (Mock Mode)...
start "Analysis Worker 1 (Mock)" cmd /k "cd /d %~dp0analysis-worker && set WORKER_INSTANCE_ID=worker-1 && set LOG_FILE=logs/analysis-worker-1.log && set USE_MOCK_MODEL=true && set WORKER_CONCURRENCY=5 && npm start"
timeout /t 3 /nobreak >nul

REM Start Analysis Worker 2
echo Starting Analysis Worker 2 (Mock Mode)...
start "Analysis Worker 2 (Mock)" cmd /k "cd /d %~dp0analysis-worker && set WORKER_INSTANCE_ID=worker-2 && set LOG_FILE=logs/analysis-worker-2.log && set USE_MOCK_MODEL=true && set WORKER_CONCURRENCY=5 && npm start"
timeout /t 3 /nobreak >nul

REM Start Analysis Worker 3
echo Starting Analysis Worker 3 (Mock Mode)...
start "Analysis Worker 3 (Mock)" cmd /k "cd /d %~dp0analysis-worker && set WORKER_INSTANCE_ID=worker-3 && set LOG_FILE=logs/analysis-worker-3.log && set USE_MOCK_MODEL=true && set WORKER_CONCURRENCY=5 && npm start"
timeout /t 3 /nobreak >nul

REM Start Analysis Worker 4
echo Starting Analysis Worker 4 (Mock Mode)...
start "Analysis Worker 4 (Mock)" cmd /k "cd /d %~dp0analysis-worker && set WORKER_INSTANCE_ID=worker-4 && set LOG_FILE=logs/analysis-worker-4.log && set USE_MOCK_MODEL=true && set WORKER_CONCURRENCY=5 && npm start"
timeout /t 3 /nobreak >nul

REM Start Analysis Worker 5
echo Starting Analysis Worker 5 (Mock Mode)...
start "Analysis Worker 5 (Mock)" cmd /k "cd /d %~dp0analysis-worker && set WORKER_INSTANCE_ID=worker-5 && set LOG_FILE=logs/analysis-worker-5.log && set USE_MOCK_MODEL=true && set WORKER_CONCURRENCY=5 && npm start"
timeout /t 3 /nobreak >nul

echo.
echo All %WORKER_COUNT% analysis workers started in MOCK MODE!
echo.

echo ========================================
echo ATMA Backend - All Services Running (MOCK MODE)
echo ========================================
echo.
echo Core Services:
echo - Auth Service (Port 3001)
echo - Archive Service (Port 3002) - Enhanced DB Pool (%DB_POOL_MAX% connections)
echo - Assessment Service (Port 3003)
echo - Notification Service (Port 3005)
echo - API Gateway (Port 3000) - Main Entry Point
echo.
echo Analysis Workers (MOCK MODE):
echo - Worker 1: analysis-worker-1.log (Mock AI)
echo - Worker 2: analysis-worker-2.log (Mock AI)
echo - Worker 3: analysis-worker-3.log (Mock AI)
echo - Worker 4: analysis-worker-4.log (Mock AI)
echo - Worker 5: analysis-worker-5.log (Mock AI)
echo.
echo Total Processing Capacity: 25 concurrent assessment jobs
echo AI Processing: MOCK MODE (No rate limits, fast processing)
echo Database Pool: %DB_POOL_MAX% connections (High concurrency support)
echo.
echo API Endpoints:
echo   http://localhost:3000/api/health (Health check)
echo   http://localhost:3000/api/auth/login (Login)
echo   http://localhost:3000/api/assessment/submit (Submit assessment)
echo.

REM Wait a bit for services to start
echo Waiting for services to initialize...
timeout /t 10 /nobreak >nul

echo ========================================
echo Service Status Check
echo ========================================
echo.

REM Check if services are listening on ports
echo Checking service ports...
netstat -an | findstr ":3001 " >nul && echo Auth Service (3001) - Listening || echo Auth Service (3001) - Not Listening
netstat -an | findstr ":3002 " >nul && echo Archive Service (3002) - Listening || echo Archive Service (3002) - Not Listening  
netstat -an | findstr ":3003 " >nul && echo Assessment Service (3003) - Listening || echo Assessment Service (3003) - Not Listening
netstat -an | findstr ":3005 " >nul && echo Notification Service (3005) - Listening || echo Notification Service (3005) - Not Listening
netstat -an | findstr ":3000 " >nul && echo API Gateway (3000) - Listening || echo API Gateway (3000) - Not Listening

echo.
echo Node.js processes running:
for /f %%i in ('tasklist /fi "imagename eq node.exe" 2^>nul ^| find /c "node.exe"') do echo Found %%i Node.js processes

echo.
echo ========================================
echo MOCK MODE CONFIGURATION SUMMARY
echo ========================================
echo.
echo ✅ AI Service: MOCK MODE (No Gemini API rate limits)
echo ✅ Database Pool: %DB_POOL_MAX% connections (High concurrency)
echo ✅ Worker Concurrency: %WORKER_COUNT% x %WORKER_CONCURRENCY% = 25 parallel jobs
echo ✅ Processing Speed: Fast mock responses (1-3 seconds per job)
echo ✅ Queue Management: RabbitMQ with proper prefetch settings
echo.
echo 🚀 Ready for 100+ concurrent user load testing!
echo.
echo Setup complete! All services and workers should be running in MOCK MODE.
echo Check the individual command windows for detailed logs.
echo.
echo To run load test: node load-test-100-users-mock.js
echo To stop all services: taskkill /f /im node.exe
echo.
pause
