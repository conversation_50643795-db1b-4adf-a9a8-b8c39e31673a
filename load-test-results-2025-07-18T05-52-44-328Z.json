{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 1, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:52:44.329Z", "stats": {"totalUsers": 1, "successfulLogins": 1, "failedLogins": 0, "successfulSubmissions": 0, "failedSubmissions": 1, "totalResponseTime": 404, "minResponseTime": 64, "maxResponseTime": 340, "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 402", "responseTime": 64, "status": 402, "responseData": {"success": false, "error": {"code": "INSUFFICIENT_TOKENS", "message": "Insufficient token balance. Required: 1, Available: 0", "details": {"required": 1, "available": 0}}}}], "startTime": 1752817963769, "endTime": 1752817964324}, "results": [], "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 402", "responseTime": 64, "status": 402, "responseData": {"success": false, "error": {"code": "INSUFFICIENT_TOKENS", "message": "Insufficient token balance. Required: 1, Available: 0", "details": {"required": 1, "available": 0}}}}]}