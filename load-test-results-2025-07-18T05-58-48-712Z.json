{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 100, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:58:48.713Z", "stats": {"totalUsers": 100, "successfulLogins": 89, "failedLogins": 11, "successfulSubmissions": 89, "failedSubmissions": 0, "totalResponseTime": 2203070, "minResponseTime": 714, "maxResponseTime": 23740, "errors": [{"userIndex": 90, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 91, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 92, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 93, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 94, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 95, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 96, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 97, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 98, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 99, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 100, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}], "startTime": 1752818278631, "endTime": 1752818328709}, "results": [{"userIndex": 1, "success": true, "loginTime": 18658, "submitTime": 6698, "queuePosition": 5, "jobId": "075acd59-5b33-45db-bcbb-0ed8b300316f"}, {"userIndex": 2, "success": true, "loginTime": 18659, "submitTime": 6712, "queuePosition": 14, "jobId": "1cf68551-f802-4bf7-b22c-623bcd29cd74"}, {"userIndex": 3, "success": true, "loginTime": 18658, "submitTime": 6713, "queuePosition": 14, "jobId": "445cd6cd-7dfe-44e5-b434-5f499d5f3359"}, {"userIndex": 4, "success": true, "loginTime": 18661, "submitTime": 6784, "queuePosition": 31, "jobId": "fd62dfeb-e33e-498c-9ff0-eb5e10018883"}, {"userIndex": 5, "success": true, "loginTime": 22430, "submitTime": 2171, "queuePosition": 94, "jobId": "0e34edfd-c98e-4306-b74e-fbd0fb75e580"}, {"userIndex": 6, "success": true, "loginTime": 18665, "submitTime": 6707, "queuePosition": 14, "jobId": "87beb702-2111-4ea6-acb6-0321833a5b20"}, {"userIndex": 7, "success": true, "loginTime": 18664, "submitTime": 6705, "queuePosition": 14, "jobId": "1091abba-7056-49f6-a0c1-aa013f616a59"}, {"userIndex": 8, "success": true, "loginTime": 18664, "submitTime": 6711, "queuePosition": 15, "jobId": "1cb91808-fc3c-4516-85b7-1e14a4b557e6"}, {"userIndex": 9, "success": true, "loginTime": 18663, "submitTime": 6713, "queuePosition": 15, "jobId": "ab2ac780-9327-431b-b2c8-80234c817d21"}, {"userIndex": 10, "success": true, "loginTime": 18660, "submitTime": 6730, "queuePosition": 15, "jobId": "55be8192-7dd5-4a6c-8ef8-3b3e9d96f2e3"}, {"userIndex": 11, "success": true, "loginTime": 18657, "submitTime": 6748, "queuePosition": 21, "jobId": "ae736827-6180-4270-97a3-52b1cfbe9fdd"}, {"userIndex": 12, "success": true, "loginTime": 18665, "submitTime": 6770, "queuePosition": 39, "jobId": "634e7635-6662-4b7c-a582-2201ad3bad28"}, {"userIndex": 13, "success": true, "loginTime": 18666, "submitTime": 6780, "queuePosition": 40, "jobId": "73a2b4b2-37f6-47b7-bbd2-da71e7b82441"}, {"userIndex": 14, "success": true, "loginTime": 18662, "submitTime": 6781, "queuePosition": 47, "jobId": "efc4ba17-9e7c-4547-8234-8137149dc8d0"}, {"userIndex": 15, "success": true, "loginTime": 18660, "submitTime": 6746, "queuePosition": 38, "jobId": "9dd55ea8-5382-41ab-b6ca-11deecedc42a"}, {"userIndex": 16, "success": true, "loginTime": 18660, "submitTime": 6745, "queuePosition": 38, "jobId": "5986bd42-6830-471f-8c3f-5c96f620b077"}, {"userIndex": 17, "success": true, "loginTime": 18661, "submitTime": 6839, "queuePosition": 63, "jobId": "dd9de9b2-5f1e-4cc5-a827-fd08ef48fe14"}, {"userIndex": 18, "success": true, "loginTime": 18653, "submitTime": 6741, "queuePosition": 38, "jobId": "5313f1f1-0831-4096-b212-44b0bd20dee1"}, {"userIndex": 19, "success": true, "loginTime": 18674, "submitTime": 6719, "queuePosition": 38, "jobId": "65054ffa-102a-4c33-9fb1-1671ae0e2f76"}, {"userIndex": 20, "success": true, "loginTime": 18668, "submitTime": 6718, "queuePosition": 38, "jobId": "11bb7da3-d0b8-4edc-b61a-e651853d007d"}, {"userIndex": 21, "success": true, "loginTime": 18668, "submitTime": 6717, "queuePosition": 38, "jobId": "44e28cbd-062a-4536-a642-873683814559"}, {"userIndex": 22, "success": true, "loginTime": 18660, "submitTime": 6716, "queuePosition": 38, "jobId": "2691f6a1-4470-465f-b27e-4772419e42d1"}, {"userIndex": 23, "success": true, "loginTime": 23299, "submitTime": 2082, "queuePosition": 39, "jobId": "0c6f844f-4bfb-463f-937a-c9bf8033449e"}, {"userIndex": 24, "success": true, "loginTime": 18658, "submitTime": 6716, "queuePosition": 38, "jobId": "1aeabfc3-aca4-4ba0-a1ae-b73640cc376b"}, {"userIndex": 25, "success": true, "loginTime": 18658, "submitTime": 6718, "queuePosition": 38, "jobId": "4b872ecd-3a1d-4c2d-9288-304660542e18"}, {"userIndex": 26, "success": true, "loginTime": 23294, "submitTime": 2083, "queuePosition": 39, "jobId": "f130f04a-fc4a-4e4f-89aa-fdaf22f0b196"}, {"userIndex": 27, "success": true, "loginTime": 23289, "submitTime": 2100, "queuePosition": 48, "jobId": "71a7e9ba-5caa-410b-9194-6374a9831ee3"}, {"userIndex": 28, "success": true, "loginTime": 23292, "submitTime": 2097, "queuePosition": 48, "jobId": "2c2bbc25-47cb-4245-b538-00f66438f5a9"}, {"userIndex": 29, "success": true, "loginTime": 23295, "submitTime": 2094, "queuePosition": 48, "jobId": "fa51d1ad-1428-4d11-8f04-aebc5c1f9eab"}, {"userIndex": 30, "success": true, "loginTime": 23298, "submitTime": 2160, "queuePosition": 72, "jobId": "1695b2cf-7a55-42bb-98b0-30f43804b1cd"}, {"userIndex": 31, "success": true, "loginTime": 23299, "submitTime": 2165, "queuePosition": 77, "jobId": "46d63b87-8e53-4880-9736-75771af3e357"}, {"userIndex": 32, "success": true, "loginTime": 23300, "submitTime": 2086, "queuePosition": 48, "jobId": "3d104a5e-2895-4f87-bfd0-f355c6721a45"}, {"userIndex": 33, "success": true, "loginTime": 23301, "submitTime": 2085, "queuePosition": 48, "jobId": "121dafd4-bc54-4b1a-85a8-c022c9d9342e"}, {"userIndex": 34, "success": true, "loginTime": 22431, "submitTime": 2076, "queuePosition": 48, "jobId": "d67c88c9-7379-45ad-ab00-8a9116b5803f"}, {"userIndex": 35, "success": true, "loginTime": 22429, "submitTime": 2079, "queuePosition": 48, "jobId": "ae35fcb9-9efa-402d-8a8b-84a063aff043"}, {"userIndex": 36, "success": true, "loginTime": 22429, "submitTime": 2092, "queuePosition": 49, "jobId": "712fec05-d2be-46dd-a9ff-4fc9cd01b476"}, {"userIndex": 37, "success": true, "loginTime": 22427, "submitTime": 2109, "queuePosition": 56, "jobId": "c14bafcb-6bd7-4513-98f0-7dc27497b1b2"}, {"userIndex": 38, "success": true, "loginTime": 22424, "submitTime": 2174, "queuePosition": 94, "jobId": "aecd4a6a-2e44-44d1-8d4e-4b610bb340a9"}, {"userIndex": 39, "success": true, "loginTime": 22421, "submitTime": 2152, "queuePosition": 79, "jobId": "9813cf69-cd4d-4bcd-ac65-7ca0622c959c"}, {"userIndex": 40, "success": true, "loginTime": 22431, "submitTime": 2154, "queuePosition": 86, "jobId": "08e75cf6-258c-46c7-8275-80fc6fbc33b5"}, {"userIndex": 41, "success": true, "loginTime": 22432, "submitTime": 2172, "queuePosition": 94, "jobId": "125c883a-40db-467a-abf0-368fe1a29530"}, {"userIndex": 42, "success": true, "loginTime": 22426, "submitTime": 2172, "queuePosition": 94, "jobId": "bf803bb0-d519-4ed7-a89e-90e85d8c1332"}, {"userIndex": 43, "success": true, "loginTime": 22426, "submitTime": 2153, "queuePosition": 94, "jobId": "bd46eaed-3a4f-4064-8b72-d464fb9c7094"}, {"userIndex": 44, "success": true, "loginTime": 22421, "submitTime": 2153, "queuePosition": 94, "jobId": "75720f48-781d-432c-872d-2b33d551b1e9"}, {"userIndex": 45, "success": true, "loginTime": 23378, "submitTime": 751, "queuePosition": 94, "jobId": "d4205c03-ba43-458f-964d-4af937cc9069"}, {"userIndex": 46, "success": true, "loginTime": 22415, "submitTime": 2144, "queuePosition": 94, "jobId": "d158010e-9bd6-4f14-8917-453fdcd46193"}, {"userIndex": 47, "success": true, "loginTime": 22397, "submitTime": 2145, "queuePosition": 94, "jobId": "7a570499-5cc9-4ba9-85e6-79352ad64a78"}, {"userIndex": 48, "success": true, "loginTime": 22396, "submitTime": 2150, "queuePosition": 94, "jobId": "e4117637-03a6-4f9f-8688-7a23a3b200a6"}, {"userIndex": 49, "success": true, "loginTime": 22394, "submitTime": 2150, "queuePosition": 94, "jobId": "57940597-0b2d-4917-aa56-684525979899"}, {"userIndex": 50, "success": true, "loginTime": 22393, "submitTime": 2149, "queuePosition": 94, "jobId": "cbbd7082-0ab2-428e-b49b-e3f6dd16f40a"}, {"userIndex": 51, "success": true, "loginTime": 22407, "submitTime": 2133, "queuePosition": 94, "jobId": "c79e8b69-f044-42c8-beae-ba0856d7c72c"}, {"userIndex": 52, "success": true, "loginTime": 22407, "submitTime": 2131, "queuePosition": 94, "jobId": "f443a580-cf97-4b72-b396-1d0df5764b6f"}, {"userIndex": 53, "success": true, "loginTime": 22403, "submitTime": 2132, "queuePosition": 94, "jobId": "35d6ed05-260c-46eb-8f89-0864a890fe94"}, {"userIndex": 54, "success": true, "loginTime": 22404, "submitTime": 2131, "queuePosition": 94, "jobId": "d39eec2f-c8ba-4f7a-ae92-aa73aa2420b1"}, {"userIndex": 55, "success": true, "loginTime": 22402, "submitTime": 2130, "queuePosition": 94, "jobId": "740986fc-8f66-4b9b-847e-eaef2fb29c48"}, {"userIndex": 56, "success": true, "loginTime": 22403, "submitTime": 2129, "queuePosition": 94, "jobId": "cba7a8bd-c8df-4486-aa80-83f77bd9e522"}, {"userIndex": 57, "success": true, "loginTime": 22399, "submitTime": 2141, "queuePosition": 94, "jobId": "e700dbd3-6a41-4e7a-862d-0725937e4901"}, {"userIndex": 58, "success": true, "loginTime": 22400, "submitTime": 2141, "queuePosition": 94, "jobId": "65d348d3-a1eb-42dd-bb45-c980c9d5a962"}, {"userIndex": 59, "success": true, "loginTime": 22394, "submitTime": 2130, "queuePosition": 94, "jobId": "712f9859-a4a6-4d45-a699-e003d3913772"}, {"userIndex": 60, "success": true, "loginTime": 22394, "submitTime": 2129, "queuePosition": 94, "jobId": "dc2bdc68-99cb-47e8-9962-96cbd5576aea"}, {"userIndex": 61, "success": true, "loginTime": 22394, "submitTime": 2128, "queuePosition": 94, "jobId": "4119f57d-0dfd-4e3d-840c-4ce761795d7a"}, {"userIndex": 62, "success": true, "loginTime": 22389, "submitTime": 2128, "queuePosition": 94, "jobId": "35e93aa0-2d44-40ab-9ece-499535aecb92"}, {"userIndex": 63, "success": true, "loginTime": 22388, "submitTime": 2128, "queuePosition": 94, "jobId": "d7f33edc-3820-4d9f-9a5f-925fe51a0eb7"}, {"userIndex": 64, "success": true, "loginTime": 22387, "submitTime": 2128, "queuePosition": 94, "jobId": "c6f15b3a-1bdd-40f0-bc32-79a91fe66ea8"}, {"userIndex": 65, "success": true, "loginTime": 22383, "submitTime": 2127, "queuePosition": 94, "jobId": "2e7ac7be-8ee0-4ed0-bce4-b1905ee251a9"}, {"userIndex": 66, "success": true, "loginTime": 23735, "submitTime": 774, "queuePosition": 94, "jobId": "ac6699d1-f625-440c-9ea1-1d297259d5c4"}, {"userIndex": 67, "success": true, "loginTime": 23736, "submitTime": 776, "queuePosition": 94, "jobId": "9c0c05fb-75b4-4bcc-bb37-7a5abc305fcd"}, {"userIndex": 68, "success": true, "loginTime": 23740, "submitTime": 773, "queuePosition": 94, "jobId": "14e0a045-e16c-4112-bf15-d3ecdd0c8d4d"}, {"userIndex": 69, "success": true, "loginTime": 23735, "submitTime": 768, "queuePosition": 94, "jobId": "dab43730-ffea-48f9-bc92-f31a568d7d4c"}, {"userIndex": 70, "success": true, "loginTime": 23738, "submitTime": 765, "queuePosition": 94, "jobId": "76ea8fec-c1d2-4bae-9563-d365598d1d44"}, {"userIndex": 71, "success": true, "loginTime": 23381, "submitTime": 769, "queuePosition": 94, "jobId": "60c39048-ea40-42a5-a36a-56adff6f8568"}, {"userIndex": 72, "success": true, "loginTime": 23386, "submitTime": 757, "queuePosition": 94, "jobId": "ec7ebbee-0f7a-49aa-8152-e24cd7a84251"}, {"userIndex": 73, "success": true, "loginTime": 23384, "submitTime": 757, "queuePosition": 94, "jobId": "b48d5856-76d5-436f-b03a-0315d3f6ae0b"}, {"userIndex": 74, "success": true, "loginTime": 23381, "submitTime": 757, "queuePosition": 94, "jobId": "8e1bb4f6-483f-448b-8fac-5d7033262a10"}, {"userIndex": 75, "success": true, "loginTime": 23378, "submitTime": 757, "queuePosition": 94, "jobId": "480b2d38-8ec9-4d6c-aeca-e0ae9dacfda3"}, {"userIndex": 76, "success": true, "loginTime": 23377, "submitTime": 757, "queuePosition": 94, "jobId": "aca2ec2e-23f5-4a25-88ec-14eeefccf989"}, {"userIndex": 77, "success": true, "loginTime": 23382, "submitTime": 754, "queuePosition": 94, "jobId": "63324a70-4815-47db-9059-26cbe6b7827e"}, {"userIndex": 78, "success": true, "loginTime": 23381, "submitTime": 757, "queuePosition": 94, "jobId": "17309e45-fdbe-42c1-ac4b-15f82ca9f20a"}, {"userIndex": 79, "success": true, "loginTime": 23375, "submitTime": 750, "queuePosition": 94, "jobId": "4f8cae1c-63ca-43ba-a556-69800bb8611a"}, {"userIndex": 80, "success": true, "loginTime": 23374, "submitTime": 750, "queuePosition": 94, "jobId": "6484e376-b5b0-4efb-a0ef-2069dd8823ac"}, {"userIndex": 81, "success": true, "loginTime": 23373, "submitTime": 749, "queuePosition": 94, "jobId": "c795b45c-2632-4666-b777-1ba344e0451e"}, {"userIndex": 82, "success": true, "loginTime": 23372, "submitTime": 748, "queuePosition": 94, "jobId": "29260f0f-7ceb-4641-b9c7-e83aebddbdeb"}, {"userIndex": 83, "success": true, "loginTime": 23383, "submitTime": 722, "queuePosition": 94, "jobId": "76809e44-e59c-493d-95a3-9894332506c3"}, {"userIndex": 84, "success": true, "loginTime": 23365, "submitTime": 718, "queuePosition": 94, "jobId": "fdfb9a0c-e425-4b7e-bde8-ebd227ae284f"}, {"userIndex": 85, "success": true, "loginTime": 23365, "submitTime": 718, "queuePosition": 94, "jobId": "3631f723-5870-47db-8dce-705342db08b2"}, {"userIndex": 86, "success": true, "loginTime": 23365, "submitTime": 715, "queuePosition": 94, "jobId": "a8a10074-8238-4e22-ab4e-a3ca44ef3dec"}, {"userIndex": 87, "success": true, "loginTime": 23362, "submitTime": 714, "queuePosition": 94, "jobId": "b5e86fcd-4edc-4745-ae42-b290aa903de8"}, {"userIndex": 88, "success": true, "loginTime": 23362, "submitTime": 720, "queuePosition": 94, "jobId": "28179c29-332a-4082-90a3-5a0238bc6de7"}, {"userIndex": 89, "success": true, "loginTime": 23362, "submitTime": 719, "queuePosition": 94, "jobId": "db04c029-b7e4-4094-b025-33464723650b"}], "errors": [{"userIndex": 90, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 91, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 92, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 93, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 94, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 95, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 96, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 97, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 98, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 99, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}, {"userIndex": 100, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT", "responseData": null}]}