/**
 * <PERSON><PERSON><PERSON> to add tokens to existing load test users
 */

const axios = require('axios');

const CONFIG = {
  AUTH_SERVICE_URL: 'http://localhost:3001',
  INTERNAL_SERVICE_KEY: 'atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7',
  TOKENS_TO_ADD: 10,
  MAX_USERS: 100
};

/**
 * Add tokens to a specific user
 */
async function addTokensToUser(userId, amount) {
  try {
    const response = await axios.put(`${CONFIG.AUTH_SERVICE_URL}/auth/token-balance`, {
      userId: userId,
      amount: amount,
      operation: 'add'
    }, {
      headers: {
        'Authorization': `Bearer ${CONFIG.INTERNAL_SERVICE_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * Get user ID by email
 */
async function getUserByEmail(email) {
  try {
    // We'll need to query the database directly since there's no public endpoint
    // For now, let's try to login to get user info
    const response = await axios.post(`${CONFIG.AUTH_SERVICE_URL}/auth/login`, {
      email: email,
      password: 'LoadTest123!'
    }, {
      timeout: 10000
    });

    if (response.data.success) {
      return {
        success: true,
        userId: response.data.data.user.id,
        currentBalance: response.data.data.user.token_balance
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * Main function to add tokens to all load test users
 */
async function addTokensToLoadTestUsers() {
  console.log('🚀 Starting token addition for load test users...');
  console.log(`📊 Configuration:`);
  console.log(`   - Auth Service: ${CONFIG.AUTH_SERVICE_URL}`);
  console.log(`   - Tokens to add: ${CONFIG.TOKENS_TO_ADD}`);
  console.log(`   - Max users: ${CONFIG.MAX_USERS}`);
  console.log('');

  let successCount = 0;
  let failureCount = 0;
  const results = [];

  for (let i = 1; i <= CONFIG.MAX_USERS; i++) {
    const email = `loadtest_user_${i}@example.com`;
    
    try {
      // Get user info
      const userInfo = await getUserByEmail(email);
      
      if (!userInfo.success) {
        console.log(`❌ User ${i}: Failed to get user info - ${userInfo.error.message || 'Unknown error'}`);
        failureCount++;
        continue;
      }

      // Add tokens
      const addResult = await addTokensToUser(userInfo.userId, CONFIG.TOKENS_TO_ADD);
      
      if (addResult.success) {
        const newBalance = addResult.data.data.new_balance;
        console.log(`✅ User ${i}: Added ${CONFIG.TOKENS_TO_ADD} tokens (${userInfo.currentBalance} → ${newBalance})`);
        successCount++;
        
        results.push({
          userIndex: i,
          email: email,
          userId: userInfo.userId,
          previousBalance: userInfo.currentBalance,
          newBalance: newBalance,
          tokensAdded: CONFIG.TOKENS_TO_ADD
        });
      } else {
        console.log(`❌ User ${i}: Failed to add tokens - ${addResult.error.message || 'Unknown error'}`);
        failureCount++;
      }
      
    } catch (error) {
      console.log(`❌ User ${i}: Unexpected error - ${error.message}`);
      failureCount++;
    }

    // Small delay to avoid overwhelming the service
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('');
  console.log('='.repeat(60));
  console.log('📊 TOKEN ADDITION RESULTS');
  console.log('='.repeat(60));
  console.log(`✅ Successful: ${successCount}/${CONFIG.MAX_USERS}`);
  console.log(`❌ Failed: ${failureCount}/${CONFIG.MAX_USERS}`);
  console.log(`📈 Success Rate: ${((successCount/CONFIG.MAX_USERS)*100).toFixed(1)}%`);
  
  if (results.length > 0) {
    const totalTokensAdded = results.reduce((sum, r) => sum + r.tokensAdded, 0);
    console.log(`🪙 Total Tokens Added: ${totalTokensAdded}`);
    console.log(`💰 Average New Balance: ${(results.reduce((sum, r) => sum + r.newBalance, 0) / results.length).toFixed(1)}`);
  }

  console.log('='.repeat(60));

  return {
    successCount,
    failureCount,
    results
  };
}

// Run the script
if (require.main === module) {
  addTokensToLoadTestUsers()
    .then((result) => {
      console.log('\n🎉 Token addition completed!');
      process.exit(result.failureCount > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = {
  addTokensToLoadTestUsers,
  addTokensToUser,
  getUserByEmail
};
