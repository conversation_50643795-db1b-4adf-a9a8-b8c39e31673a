{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 10, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:43:29.142Z", "stats": {"totalUsers": 10, "successfulLogins": 0, "failedLogins": 10, "successfulSubmissions": 0, "failedSubmissions": 0, "totalResponseTime": 0, "minResponseTime": null, "maxResponseTime": 0, "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 4, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 5, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 6, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 7, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 8, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 9, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 10, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 2, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 3, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}], "startTime": 1752817409012, "endTime": 1752817409137}, "results": [], "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 2, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 3, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 4, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 5, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 6, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 7, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 8, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 9, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}, {"userIndex": 10, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400}]}