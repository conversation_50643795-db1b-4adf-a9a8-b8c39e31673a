/**
 * Load Testing Script - 100 Concurrent Users with Mock AI
 * 
 * Script untuk mensimulasikan 100 user dengan mock AI service
 * untuk menghindari rate limiting Gemini API
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Configuration
const CONFIG = {
  API_GATEWAY_URL: process.env.API_GATEWAY_URL || 'http://localhost:3000',
  CONCURRENT_USERS: parseInt(process.env.CONCURRENT_USERS || '100'),
  ASSESSMENT_DELAY: parseInt(process.env.ASSESSMENT_DELAY || '0'), // ms delay between submissions
  USE_MOCK_AI: true, // Force mock AI usage
  NETWORK_SIMULATION: {
    // Simulasi jaringan yang sama - IP range yang sama
    baseIP: '192.168.1.',
    startIP: 100,
    endIP: 199
  },
  TIMEOUT: 60000, // 60 seconds timeout (increased for mock processing)
  RETRY_ATTEMPTS: 3,
  // Rate limiting aware configuration
  RATE_LIMIT_AWARE: false // Disabled since we're using mock
};

// Sample assessment data
const SAMPLE_ASSESSMENT = {
  riasec: {
    realistic: Math.floor(Math.random() * 100),
    investigative: Math.floor(Math.random() * 100),
    artistic: Math.floor(Math.random() * 100),
    social: Math.floor(Math.random() * 100),
    enterprising: Math.floor(Math.random() * 100),
    conventional: Math.floor(Math.random() * 100)
  },
  ocean: {
    openness: Math.floor(Math.random() * 100),
    conscientiousness: Math.floor(Math.random() * 100),
    extraversion: Math.floor(Math.random() * 100),
    agreeableness: Math.floor(Math.random() * 100),
    neuroticism: Math.floor(Math.random() * 100)
  },
  viaIs: {
    creativity: Math.floor(Math.random() * 100),
    curiosity: Math.floor(Math.random() * 100),
    judgment: Math.floor(Math.random() * 100),
    loveOfLearning: Math.floor(Math.random() * 100),
    perspective: Math.floor(Math.random() * 100),
    bravery: Math.floor(Math.random() * 100),
    perseverance: Math.floor(Math.random() * 100),
    honesty: Math.floor(Math.random() * 100),
    zest: Math.floor(Math.random() * 100),
    love: Math.floor(Math.random() * 100),
    kindness: Math.floor(Math.random() * 100),
    socialIntelligence: Math.floor(Math.random() * 100),
    teamwork: Math.floor(Math.random() * 100),
    fairness: Math.floor(Math.random() * 100),
    leadership: Math.floor(Math.random() * 100),
    forgiveness: Math.floor(Math.random() * 100),
    humility: Math.floor(Math.random() * 100),
    prudence: Math.floor(Math.random() * 100),
    selfRegulation: Math.floor(Math.random() * 100),
    appreciationOfBeauty: Math.floor(Math.random() * 100),
    gratitude: Math.floor(Math.random() * 100),
    hope: Math.floor(Math.random() * 100),
    humor: Math.floor(Math.random() * 100),
    spirituality: Math.floor(Math.random() * 100)
  }
};

// Statistics tracking
const stats = {
  totalUsers: 0,
  successfulLogins: 0,
  failedLogins: 0,
  successfulSubmissions: 0,
  failedSubmissions: 0,
  totalResponseTime: 0,
  minResponseTime: Infinity,
  maxResponseTime: 0,
  errors: [],
  startTime: null,
  endTime: null
};

/**
 * Generate random IP address in the same network
 */
function generateNetworkIP(userIndex) {
  const { baseIP, startIP, endIP } = CONFIG.NETWORK_SIMULATION;
  const ipSuffix = startIP + (userIndex % (endIP - startIP + 1));
  return `${baseIP}${ipSuffix}`;
}

/**
 * Create test user credentials
 */
function createTestUser(index) {
  // Use timestamp to ensure unique users for each test run
  const timestamp = Date.now();
  return {
    email: `mocktest_${timestamp}_${index}@example.com`,
    password: 'MockTest123!',
    name: `Mock Test User ${index}`,
    ip: generateNetworkIP(index)
  };
}

/**
 * Register a test user
 */
async function registerUser(userData) {
  try {
    const response = await axios.post(`${CONFIG.API_GATEWAY_URL}/auth/register`, {
      email: userData.email,
      password: userData.password
    }, {
      timeout: CONFIG.TIMEOUT,
      headers: {
        'X-Forwarded-For': userData.ip,
        'X-Real-IP': userData.ip
      }
    });
    
    return response.data;
  } catch (error) {
    if (error.response?.status === 409 ||
        (error.response?.status === 400 && error.response?.data?.error?.code === 'EMAIL_EXISTS')) {
      // User already exists, that's okay for load testing
      return { success: true, message: 'User already exists' };
    }
    throw error;
  }
}

/**
 * Login user and get JWT token
 */
async function loginUser(userData) {
  const startTime = Date.now();
  
  try {
    const response = await axios.post(`${CONFIG.API_GATEWAY_URL}/auth/login`, {
      email: userData.email,
      password: userData.password
    }, {
      timeout: CONFIG.TIMEOUT,
      headers: {
        'X-Forwarded-For': userData.ip,
        'X-Real-IP': userData.ip
      }
    });
    
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    return {
      success: true,
      token: response.data.data.token,
      user: response.data.data.user,
      responseTime
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    throw {
      ...error,
      responseTime,
      userData: userData.email
    };
  }
}

/**
 * Submit assessment data
 */
async function submitAssessment(token, userData, assessmentData) {
  const startTime = Date.now();
  
  try {
    const response = await axios.post(`${CONFIG.API_GATEWAY_URL}/assessments/submit`, 
      assessmentData, 
      {
        timeout: CONFIG.TIMEOUT,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'X-Forwarded-For': userData.ip,
          'X-Real-IP': userData.ip,
          'X-Use-Mock-AI': 'true' // Force mock AI usage
        }
      }
    );
    
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    return {
      success: true,
      jobId: response.data.data.jobId,
      queuePosition: response.data.data.queuePosition,
      responseTime
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    throw {
      ...error,
      responseTime,
      userData: userData.email
    };
  }
}

/**
 * Update response time statistics
 */
function updateResponseTimeStats(responseTime) {
  stats.totalResponseTime += responseTime;
  stats.minResponseTime = Math.min(stats.minResponseTime, responseTime);
  stats.maxResponseTime = Math.max(stats.maxResponseTime, responseTime);
}

/**
 * Generate random assessment data for each user
 */
function generateRandomAssessment() {
  const assessment = JSON.parse(JSON.stringify(SAMPLE_ASSESSMENT));

  // Randomize all values
  Object.keys(assessment).forEach(category => {
    Object.keys(assessment[category]).forEach(trait => {
      assessment[category][trait] = Math.floor(Math.random() * 100);
    });
  });

  return assessment;
}

/**
 * Simulate single user journey
 */
async function simulateUser(userIndex) {
  const userData = createTestUser(userIndex);
  let token = null;
  
  try {
    // Step 1: Register user (if not exists)
    await registerUser(userData);
    
    // Step 2: Login
    const loginResult = await loginUser(userData);
    token = loginResult.token;
    stats.successfulLogins++;
    
    // Step 3: Submit assessment with delay
    if (CONFIG.ASSESSMENT_DELAY > 0) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * CONFIG.ASSESSMENT_DELAY));
    }
    
    const assessmentData = generateRandomAssessment();
    const submitResult = await submitAssessment(token, userData, assessmentData);
    stats.successfulSubmissions++;
    
    console.log(`✅ User ${userIndex}: Login ${loginResult.responseTime}ms, Submit ${submitResult.responseTime}ms, Queue: ${submitResult.queuePosition}`);
    
    return {
      userIndex,
      success: true,
      loginTime: loginResult.responseTime,
      submitTime: submitResult.responseTime,
      queuePosition: submitResult.queuePosition,
      jobId: submitResult.jobId
    };
    
  } catch (error) {
    if (!token) {
      stats.failedLogins++;
    } else {
      stats.failedSubmissions++;
    }
    
    const errorInfo = {
      userIndex,
      email: userData.email,
      error: error.message || error.toString(),
      responseTime: error.responseTime || 0,
      status: error.response?.status || 'TIMEOUT',
      responseData: error.response?.data || null
    };

    stats.errors.push(errorInfo);
    console.log(`❌ User ${userIndex}: ${errorInfo.error} (${errorInfo.status})`);
    if (errorInfo.responseData) {
      console.log(`   Response: ${JSON.stringify(errorInfo.responseData)}`);
    }
    
    return {
      userIndex,
      success: false,
      error: errorInfo
    };
  }
}

// Export for use in other scripts
module.exports = {
  simulateUser,
  CONFIG,
  stats,
  generateRandomAssessment
};

// Run if called directly
if (require.main === module) {
  // Import the main load test function from the original script
  const { runLoadTest } = require('./load-test-100-users');
  
  // Override config for mock mode
  Object.assign(CONFIG, {
    GEMINI_TIER: 'mock',
    USE_MOCK_AI: true
  });
  
  console.log('🚀 Starting load test with MOCK AI SERVICE');
  console.log('📡 API Gateway:', CONFIG.API_GATEWAY_URL);
  console.log('🤖 AI Mode: MOCK (No rate limits)');
  console.log('👥 Concurrent Users:', CONFIG.CONCURRENT_USERS);
  console.log('');
  
  runLoadTest().catch(error => {
    console.error('❌ Load test failed:', error.message);
    process.exit(1);
  });
}
