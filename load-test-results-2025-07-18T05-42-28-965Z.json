{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 100, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:42:28.966Z", "stats": {"totalUsers": 100, "successfulLogins": 88, "failedLogins": 12, "successfulSubmissions": 0, "failedSubmissions": 88, "totalResponseTime": 2156073, "minResponseTime": 242, "maxResponseTime": 24069, "errors": [{"userIndex": 89, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 90, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 91, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 92, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 93, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 94, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 95, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 96, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 97, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 98, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 99, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 100, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7069, "status": 400}, {"userIndex": 4, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7071, "status": 400}, {"userIndex": 2, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7072, "status": 400}, {"userIndex": 6, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7074, "status": 400}, {"userIndex": 7, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7063, "status": 400}, {"userIndex": 8, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7071, "status": 400}, {"userIndex": 9, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7074, "status": 400}, {"userIndex": 14, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7076, "status": 400}, {"userIndex": 11, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7085, "status": 400}, {"userIndex": 15, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7085, "status": 400}, {"userIndex": 12, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7205, "status": 400}, {"userIndex": 13, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7205, "status": 400}, {"userIndex": 16, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7203, "status": 400}, {"userIndex": 17, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7203, "status": 400}, {"userIndex": 22, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7203, "status": 400}, {"userIndex": 10, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7185, "status": 400}, {"userIndex": 18, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7185, "status": 400}, {"userIndex": 19, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7184, "status": 400}, {"userIndex": 20, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7184, "status": 400}, {"userIndex": 21, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7187, "status": 400}, {"userIndex": 23, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7188, "status": 400}, {"userIndex": 24, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7187, "status": 400}, {"userIndex": 25, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7224, "status": 400}, {"userIndex": 26, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7224, "status": 400}, {"userIndex": 27, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2089, "status": 400}, {"userIndex": 28, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2089, "status": 400}, {"userIndex": 32, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2088, "status": 400}, {"userIndex": 29, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2083, "status": 400}, {"userIndex": 30, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2080, "status": 400}, {"userIndex": 31, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2076, "status": 400}, {"userIndex": 3, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2075, "status": 400}, {"userIndex": 5, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2074, "status": 400}, {"userIndex": 33, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2074, "status": 400}, {"userIndex": 34, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2074, "status": 400}, {"userIndex": 37, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2066, "status": 400}, {"userIndex": 35, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2066, "status": 400}, {"userIndex": 36, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2064, "status": 400}, {"userIndex": 38, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2067, "status": 400}, {"userIndex": 39, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2068, "status": 400}, {"userIndex": 40, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2067, "status": 400}, {"userIndex": 41, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2066, "status": 400}, {"userIndex": 42, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 43, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 44, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2048, "status": 400}, {"userIndex": 45, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 46, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 47, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 48, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 49, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2052, "status": 400}, {"userIndex": 50, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2051, "status": 400}, {"userIndex": 52, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 53, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2050, "status": 400}, {"userIndex": 54, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2048, "status": 400}, {"userIndex": 55, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2028, "status": 400}, {"userIndex": 59, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2022, "status": 400}, {"userIndex": 51, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2059, "status": 400}, {"userIndex": 60, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 272, "status": 400}, {"userIndex": 62, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 268, "status": 400}, {"userIndex": 56, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2034, "status": 400}, {"userIndex": 57, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2033, "status": 400}, {"userIndex": 58, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2032, "status": 400}, {"userIndex": 63, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 265, "status": 400}, {"userIndex": 61, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 64, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 256, "status": 400}, {"userIndex": 65, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 255, "status": 400}, {"userIndex": 66, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 260, "status": 400}, {"userIndex": 67, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 68, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 69, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 258, "status": 400}, {"userIndex": 70, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 258, "status": 400}, {"userIndex": 71, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 72, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 266, "status": 400}, {"userIndex": 73, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 266, "status": 400}, {"userIndex": 75, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 271, "status": 400}, {"userIndex": 76, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 77, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 268, "status": 400}, {"userIndex": 74, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 268, "status": 400}, {"userIndex": 78, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 79, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 80, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 81, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 247, "status": 400}, {"userIndex": 83, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 245, "status": 400}, {"userIndex": 84, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 245, "status": 400}, {"userIndex": 86, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 242, "status": 400}, {"userIndex": 87, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 242, "status": 400}, {"userIndex": 88, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 243, "status": 400}, {"userIndex": 82, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 255, "status": 400}, {"userIndex": 85, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 251, "status": 400}], "startTime": 1752817297868, "endTime": 1752817348961}, "results": [], "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7069, "status": 400}, {"userIndex": 2, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7072, "status": 400}, {"userIndex": 3, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2075, "status": 400}, {"userIndex": 4, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7071, "status": 400}, {"userIndex": 5, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2074, "status": 400}, {"userIndex": 6, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7074, "status": 400}, {"userIndex": 7, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7063, "status": 400}, {"userIndex": 8, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7071, "status": 400}, {"userIndex": 9, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7074, "status": 400}, {"userIndex": 10, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7185, "status": 400}, {"userIndex": 11, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7085, "status": 400}, {"userIndex": 12, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7205, "status": 400}, {"userIndex": 13, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7205, "status": 400}, {"userIndex": 14, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7076, "status": 400}, {"userIndex": 15, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7085, "status": 400}, {"userIndex": 16, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7203, "status": 400}, {"userIndex": 17, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7203, "status": 400}, {"userIndex": 18, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7185, "status": 400}, {"userIndex": 19, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7184, "status": 400}, {"userIndex": 20, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7184, "status": 400}, {"userIndex": 21, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7187, "status": 400}, {"userIndex": 22, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7203, "status": 400}, {"userIndex": 23, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7188, "status": 400}, {"userIndex": 24, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7187, "status": 400}, {"userIndex": 25, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7224, "status": 400}, {"userIndex": 26, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 7224, "status": 400}, {"userIndex": 27, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2089, "status": 400}, {"userIndex": 28, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2089, "status": 400}, {"userIndex": 29, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2083, "status": 400}, {"userIndex": 30, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2080, "status": 400}, {"userIndex": 31, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2076, "status": 400}, {"userIndex": 32, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2088, "status": 400}, {"userIndex": 33, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2074, "status": 400}, {"userIndex": 34, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2074, "status": 400}, {"userIndex": 35, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2066, "status": 400}, {"userIndex": 36, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2064, "status": 400}, {"userIndex": 37, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2066, "status": 400}, {"userIndex": 38, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2067, "status": 400}, {"userIndex": 39, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2068, "status": 400}, {"userIndex": 40, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2067, "status": 400}, {"userIndex": 41, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2066, "status": 400}, {"userIndex": 42, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 43, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 44, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2048, "status": 400}, {"userIndex": 45, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 46, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 47, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 48, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 49, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2052, "status": 400}, {"userIndex": 50, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2051, "status": 400}, {"userIndex": 51, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2059, "status": 400}, {"userIndex": 52, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2049, "status": 400}, {"userIndex": 53, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2050, "status": 400}, {"userIndex": 54, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2048, "status": 400}, {"userIndex": 55, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2028, "status": 400}, {"userIndex": 56, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2034, "status": 400}, {"userIndex": 57, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2033, "status": 400}, {"userIndex": 58, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2032, "status": 400}, {"userIndex": 59, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 2022, "status": 400}, {"userIndex": 60, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 272, "status": 400}, {"userIndex": 61, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 62, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 268, "status": 400}, {"userIndex": 63, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 265, "status": 400}, {"userIndex": 64, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 256, "status": 400}, {"userIndex": 65, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 255, "status": 400}, {"userIndex": 66, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 260, "status": 400}, {"userIndex": 67, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 68, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 69, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 258, "status": 400}, {"userIndex": 70, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 258, "status": 400}, {"userIndex": 71, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 259, "status": 400}, {"userIndex": 72, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 266, "status": 400}, {"userIndex": 73, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 266, "status": 400}, {"userIndex": 74, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 268, "status": 400}, {"userIndex": 75, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 271, "status": 400}, {"userIndex": 76, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 77, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 268, "status": 400}, {"userIndex": 78, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 79, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 80, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 269, "status": 400}, {"userIndex": 81, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 247, "status": 400}, {"userIndex": 82, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 255, "status": 400}, {"userIndex": 83, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 245, "status": 400}, {"userIndex": 84, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 245, "status": 400}, {"userIndex": 85, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 251, "status": 400}, {"userIndex": 86, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 242, "status": 400}, {"userIndex": 87, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 242, "status": 400}, {"userIndex": 88, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 243, "status": 400}, {"userIndex": 89, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 90, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 91, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 92, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 93, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 94, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 95, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 96, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 97, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 98, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 99, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}, {"userIndex": 100, "email": "<EMAIL>", "error": "timeout of 30000ms exceeded", "responseTime": 0, "status": "TIMEOUT"}]}