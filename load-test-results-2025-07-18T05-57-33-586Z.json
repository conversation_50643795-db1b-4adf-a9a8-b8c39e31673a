{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 1, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:57:33.587Z", "stats": {"totalUsers": 1, "successfulLogins": 1, "failedLogins": 0, "successfulSubmissions": 1, "failedSubmissions": 0, "totalResponseTime": 330, "minResponseTime": 56, "maxResponseTime": 274, "errors": [], "startTime": 1752818252913, "endTime": 1752818253584}, "results": [{"userIndex": 1, "success": true, "loginTime": 274, "submitTime": 56, "queuePosition": 0, "jobId": "15dedfb7-ff25-4892-96e2-cb9d336883b6"}], "errors": []}