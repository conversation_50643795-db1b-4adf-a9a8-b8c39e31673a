{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 1, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:45:01.830Z", "stats": {"totalUsers": 1, "successfulLogins": 0, "failedLogins": 1, "successfulSubmissions": 0, "failedSubmissions": 0, "totalResponseTime": 0, "minResponseTime": null, "maxResponseTime": 0, "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400, "responseData": {"success": false, "error": {"code": "EMAIL_EXISTS", "message": "Email already exists", "stack": "Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)", "details": "Email already exists"}}}], "startTime": 1752817501760, "endTime": 1752817501827}, "results": [], "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "Request failed with status code 400", "responseTime": 0, "status": 400, "responseData": {"success": false, "error": {"code": "EMAIL_EXISTS", "message": "Email already exists", "stack": "Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)", "details": "Email already exists"}}}]}