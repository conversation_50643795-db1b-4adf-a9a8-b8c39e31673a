{"config": {"API_GATEWAY_URL": "http://localhost:3000", "CONCURRENT_USERS": 1, "ASSESSMENT_DELAY": 0, "GEMINI_TIER": "free", "NETWORK_SIMULATION": {"baseIP": "192.168.1.", "startIP": 100, "endIP": 199}, "TIMEOUT": 30000, "RETRY_ATTEMPTS": 3, "RATE_LIMIT_AWARE": true}, "timestamp": "2025-07-18T05:54:36.220Z", "stats": {"totalUsers": 1, "successfulLogins": 0, "failedLogins": 1, "successfulSubmissions": 0, "failedSubmissions": 0, "totalResponseTime": 0, "minResponseTime": null, "maxResponseTime": 0, "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "AggregateError", "responseTime": 0, "status": "TIMEOUT", "responseData": null}], "startTime": 1752818076181, "endTime": 1752818076216}, "results": [], "errors": [{"userIndex": 1, "email": "<EMAIL>", "error": "AggregateError", "responseTime": 0, "status": "TIMEOUT", "responseData": null}]}